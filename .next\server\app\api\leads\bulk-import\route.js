const CHUNK_PUBLIC_PATH = "server/app/api/leads/bulk-import/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_c38bff2b._.js");
runtime.loadChunk("server/chunks/node_modules_zod_v4_719ed6d0._.js");
runtime.loadChunk("server/chunks/node_modules_xlsx_xlsx_mjs_2a6cf520._.js");
runtime.loadChunk("server/chunks/node_modules_c30b96ef._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__a1b5e0c4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/leads/bulk-import/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/leads/bulk-import/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/leads/bulk-import/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
