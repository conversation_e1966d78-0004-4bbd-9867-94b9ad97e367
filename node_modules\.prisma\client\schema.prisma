// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum LeadStatus {
  NEW
  CONTACTED
  INTERESTED
  CONVERTED
  LOST
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  leads Lead[]

  @@map("users")
}

model Lead {
  id        String     @id @default(cuid())
  name      String
  location  String?
  phone     String?
  email     String
  website   String?
  notes     String?
  source    String?
  status    LeadStatus @default(NEW)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // Relations
  userId    String
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  leadNotes Note[]     @relation("LeadNotes")
  followUps FollowUp[]

  @@map("leads")
}

model Note {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())

  // Relations
  leadId String
  lead   Lead   @relation("LeadNotes", fields: [leadId], references: [id], onDelete: Cascade)

  @@map("notes")
}

model FollowUp {
  id          String   @id @default(cuid())
  title       String
  description String?
  dueDate     DateTime
  completed   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  leadId String
  lead   Lead   @relation(fields: [leadId], references: [id], onDelete: Cascade)

  @@map("follow_ups")
}
