{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/lib/validations.ts"], "sourcesContent": ["import { z } from 'zod'\n\n// Auth schemas\nexport const signUpSchema = z.object({\n  name: z.string().min(2, 'Name must be at least 2 characters'),\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n})\n\nexport const signInSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(1, 'Password is required'),\n})\n\n// Lead schemas\nexport const leadSchema = z.object({\n  name: z.string().min(1, 'Name is required'),\n  location: z.string().optional(),\n  phone: z.string().optional(),\n  email: z.string().email('Invalid email address'),\n  website: z.string().url('Invalid website URL').optional().or(z.literal('')),\n  notes: z.string().optional(),\n  source: z.string().optional(),\n  status: z.enum(['NEW', 'CONTACTED', 'INTERESTED', 'CONVERTED', 'LOST']).default('NEW'),\n})\n\nexport const updateLeadSchema = leadSchema.partial()\n\n// Note schemas\nexport const noteSchema = z.object({\n  content: z.string().min(1, 'Content is required'),\n  leadId: z.string().min(1, 'Lead ID is required'),\n})\n\n// Follow-up schemas\nexport const followUpSchema = z.object({\n  title: z.string().min(1, 'Title is required'),\n  description: z.string().optional(),\n  dueDate: z.string().datetime('Invalid date format'),\n  leadId: z.string().min(1, 'Lead ID is required'),\n})\n\nexport const updateFollowUpSchema = followUpSchema.partial().extend({\n  completed: z.boolean().optional(),\n})\n\n// Bulk import schema\nexport const bulkLeadSchema = z.object({\n  leads: z.array(leadSchema.omit({ status: true })),\n})\n\nexport type SignUpInput = z.infer<typeof signUpSchema>\nexport type SignInInput = z.infer<typeof signInSchema>\nexport type LeadInput = z.infer<typeof leadSchema>\nexport type UpdateLeadInput = z.infer<typeof updateLeadSchema>\nexport type NoteInput = z.infer<typeof noteSchema>\nexport type FollowUpInput = z.infer<typeof followUpSchema>\nexport type UpdateFollowUpInput = z.infer<typeof updateFollowUpSchema>\nexport type BulkLeadInput = z.infer<typeof bulkLeadSchema>\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAGO,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEO,MAAM,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAGO,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,MAAM,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,uBAAuB,QAAQ,GAAG,EAAE,CAAC,gLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACvE,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,QAAQ,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;QAAa;QAAc;QAAa;KAAO,EAAE,OAAO,CAAC;AAClF;AAEO,MAAM,mBAAmB,WAAW,OAAO;AAG3C,MAAM,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC5B;AAGO,MAAM,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,QAAQ,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC5B;AAEO,MAAM,uBAAuB,eAAe,OAAO,GAAG,MAAM,CAAC;IAClE,WAAW,gLAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ;AACjC;AAGO,MAAM,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,OAAO,gLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC;QAAE,QAAQ;IAAK;AAChD", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/select.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface SelectProps\n  extends React.SelectHTMLAttributes<HTMLSelectElement> {}\n\nconst Select = React.forwardRef<HTMLSelectElement, SelectProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <select\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      >\n        {children}\n      </select>\n    )\n  }\n)\nSelect.displayName = \"Select\"\n\nexport { Select }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;IAChC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-white text-gray-900 shadow-sm border-gray-200\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight text-gray-900\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-gray-600\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DialogProps {\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  children: React.ReactNode\n}\n\nconst Dialog: React.FC<DialogProps> = ({ open, onOpenChange, children }) => {\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && onOpenChange) {\n        onOpenChange(false)\n      }\n    }\n\n    if (open) {\n      document.addEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'hidden'\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape)\n      document.body.style.overflow = 'unset'\n    }\n  }, [open, onOpenChange])\n\n  if (!open) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      <div \n        className=\"fixed inset-0 bg-black/50\" \n        onClick={() => onOpenChange?.(false)}\n      />\n      <div className=\"relative z-50 max-h-[90vh] overflow-auto\">\n        {children}\n      </div>\n    </div>\n  )\n}\n\nconst DialogContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, children, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"bg-background p-6 shadow-lg rounded-lg border max-w-lg w-full mx-4\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n  </div>\n))\nDialogContent.displayName = \"DialogContent\"\n\nconst DialogHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)}\n    {...props}\n  />\n))\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogTitle = React.forwardRef<\n  HTMLHeadingElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h2\n    ref={ref}\n    className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nDialogTitle.displayName = \"DialogTitle\"\n\nconst DialogDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = \"DialogDescription\"\n\nexport {\n  Dialog,\n  DialogContent,\n  DialogHeader,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;;;AAQA,MAAM,SAAgC;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE;;IACrE,6JAAA,CAAA,YAAe;4BAAC;YACd,MAAM;iDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,cAAc;wBACtC,aAAa;oBACf;gBACF;;YAEA,IAAI,MAAM;gBACR,SAAS,gBAAgB,CAAC,WAAW;gBACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;oCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;oBACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;QAAM;KAAa;IAEvB,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,yBAAA,mCAAA,aAAe;;;;;;0BAEhC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;GAhCM;KAAA;AAkCN,MAAM,8BAAgB,6JAAA,CAAA,aAAgB,OAGpC,QAAoC;QAAnC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO;yBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;kBAER;;;;;;;;AAGL,cAAc,WAAW,GAAG;AAE5B,MAAM,6BAAe,6JAAA,CAAA,aAAgB,OAGnC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/form.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Form = React.forwardRef<\n  HTMLFormElement,\n  React.FormHTMLAttributes<HTMLFormElement>\n>(({ className, ...props }, ref) => (\n  <form\n    ref={ref}\n    className={cn(\"space-y-6\", className)}\n    {...props}\n  />\n))\nForm.displayName = \"Form\"\n\nconst FormField = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"space-y-2\", className)}\n    {...props}\n  />\n))\nFormField.displayName = \"FormField\"\n\nconst FormLabel = React.forwardRef<\n  HTMLLabelElement,\n  React.LabelHTMLAttributes<HTMLLabelElement>\n>(({ className, ...props }, ref) => (\n  <label\n    ref={ref}\n    className={cn(\n      \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n      className\n    )}\n    {...props}\n  />\n))\nFormLabel.displayName = \"FormLabel\"\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm font-medium text-destructive\", className)}\n    {...props}\n  />\n))\nFormMessage.displayName = \"FormMessage\"\n\nexport { Form, FormField, FormLabel, FormMessage }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC1B,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/dashboard/notes/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { noteSchema, type NoteInput } from '@/lib/validations'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Select } from '@/components/ui/select'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'\nimport { Form, FormField, FormLabel, FormMessage } from '@/components/ui/form'\nimport { Plus, Trash2, MessageSquare } from 'lucide-react'\n\ninterface Lead {\n  id: string\n  name: string\n  email: string\n}\n\ninterface Note {\n  id: string\n  content: string\n  createdAt: string\n  leadId: string\n  lead?: Lead\n}\n\nexport default function NotesPage() {\n  const [notes, setNotes] = useState<Note[]>([])\n  const [leads, setLeads] = useState<Lead[]>([])\n  const [loading, setLoading] = useState(true)\n  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)\n  const [selectedLeadId, setSelectedLeadId] = useState('')\n\n  const form = useForm<NoteInput>({\n    resolver: zodResolver(noteSchema),\n    defaultValues: {\n      content: '',\n      leadId: '',\n    },\n  })\n\n  useEffect(() => {\n    fetchLeads()\n    fetchAllNotes()\n  }, [])\n\n  const fetchLeads = async () => {\n    try {\n      const response = await fetch('/api/leads?limit=100')\n      if (response.ok) {\n        const data = await response.json()\n        setLeads(data.leads)\n      }\n    } catch (error) {\n      // Error fetching leads - handle silently in production\n    }\n  }\n\n  const fetchAllNotes = async () => {\n    try {\n      // Since we need notes from all leads, we'll fetch them per lead\n      const response = await fetch('/api/leads?limit=100')\n      if (response.ok) {\n        const data = await response.json()\n        const allNotes: Note[] = []\n        \n        // Collect all notes from all leads\n        for (const lead of data.leads) {\n          if (lead.notes && lead.notes.length > 0) {\n            lead.notes.forEach((note: any) => {\n              allNotes.push({\n                ...note,\n                lead: {\n                  id: lead.id,\n                  name: lead.name,\n                  email: lead.email,\n                }\n              })\n            })\n          }\n        }\n        \n        // Sort by creation date (newest first)\n        allNotes.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())\n        setNotes(allNotes)\n      }\n    } catch (error) {\n      // Error fetching notes - handle silently in production\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const fetchNotesForLead = async (leadId: string) => {\n    try {\n      const response = await fetch(`/api/notes?leadId=${leadId}`)\n      if (response.ok) {\n        const data = await response.json()\n        return data\n      }\n    } catch (error) {\n      // Error fetching notes for lead - handle silently in production\n    }\n    return []\n  }\n\n  const onSubmit = async (data: NoteInput) => {\n    try {\n      const response = await fetch('/api/notes', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(data),\n      })\n\n      if (response.ok) {\n        await fetchAllNotes()\n        setIsCreateDialogOpen(false)\n        form.reset()\n      }\n    } catch (error) {\n      // Error creating note - handle silently in production\n    }\n  }\n\n  const handleDelete = async (noteId: string) => {\n    if (!confirm('Are you sure you want to delete this note?')) return\n\n    try {\n      const response = await fetch(`/api/notes/${noteId}`, {\n        method: 'DELETE',\n      })\n\n      if (response.ok) {\n        await fetchAllNotes()\n      }\n    } catch (error) {\n      // Error deleting note - handle silently in production\n    }\n  }\n\n  const filteredNotes = selectedLeadId \n    ? notes.filter(note => note.leadId === selectedLeadId)\n    : notes\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-3xl font-bold\">Notes</h1>\n        <Button onClick={() => setIsCreateDialogOpen(true)}>\n          <Plus className=\"mr-2 h-4 w-4\" />\n          Add Note\n        </Button>\n      </div>\n\n      {/* Filter */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Filter Notes</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid gap-4 md:grid-cols-2\">\n            <div>\n              <label className=\"text-sm font-medium\">Filter by Lead</label>\n              <Select\n                value={selectedLeadId}\n                onChange={(e) => setSelectedLeadId(e.target.value)}\n              >\n                <option value=\"\">All Leads</option>\n                {leads.map((lead) => (\n                  <option key={lead.id} value={lead.id}>\n                    {lead.name} ({lead.email})\n                  </option>\n                ))}\n              </Select>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Notes List */}\n      <div className=\"space-y-4\">\n        {loading ? (\n          <p>Loading notes...</p>\n        ) : filteredNotes.length === 0 ? (\n          <Card>\n            <CardContent className=\"text-center py-8\">\n              <MessageSquare className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\n              <p className=\"text-muted-foreground\">No notes found.</p>\n            </CardContent>\n          </Card>\n        ) : (\n          filteredNotes.map((note) => (\n            <Card key={note.id}>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"space-y-2 flex-1\">\n                    <div className=\"flex items-center gap-2\">\n                      <h3 className=\"font-semibold\">\n                        {note.lead?.name || 'Unknown Lead'}\n                      </h3>\n                      <span className=\"text-sm text-muted-foreground\">\n                        ({note.lead?.email || 'No email'})\n                      </span>\n                    </div>\n                    <p className=\"text-gray-700 whitespace-pre-wrap\">\n                      {note.content}\n                    </p>\n                    <p className=\"text-xs text-muted-foreground\">\n                      {new Date(note.createdAt).toLocaleString()}\n                    </p>\n                  </div>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => handleDelete(note.id)}\n                  >\n                    <Trash2 className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n\n      {/* Create Note Dialog */}\n      <Dialog \n        open={isCreateDialogOpen} \n        onOpenChange={(open) => {\n          if (!open) {\n            setIsCreateDialogOpen(false)\n            form.reset()\n          }\n        }}\n      >\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Add New Note</DialogTitle>\n          </DialogHeader>\n          <Form onSubmit={form.handleSubmit(onSubmit)}>\n            <FormField>\n              <FormLabel htmlFor=\"leadId\">Lead</FormLabel>\n              <Select {...form.register('leadId')}>\n                <option value=\"\">Select a lead</option>\n                {leads.map((lead) => (\n                  <option key={lead.id} value={lead.id}>\n                    {lead.name} ({lead.email})\n                  </option>\n                ))}\n              </Select>\n              {form.formState.errors.leadId && (\n                <FormMessage>{form.formState.errors.leadId.message}</FormMessage>\n              )}\n            </FormField>\n\n            <FormField>\n              <FormLabel htmlFor=\"content\">Note Content</FormLabel>\n              <textarea\n                id=\"content\"\n                rows={4}\n                placeholder=\"Enter your note...\"\n                className=\"flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\n                {...form.register('content')}\n              />\n              {form.formState.errors.content && (\n                <FormMessage>{form.formState.errors.content.message}</FormMessage>\n              )}\n            </FormField>\n\n            <div className=\"flex gap-2 pt-4\">\n              <Button type=\"submit\" className=\"flex-1\">\n                Create Note\n              </Button>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => {\n                  setIsCreateDialogOpen(false)\n                  form.reset()\n                }}\n              >\n                Cancel\n              </Button>\n            </div>\n          </Form>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAZA;;;;;;;;;;;AA4Be,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAa;QAC9B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE,4HAAA,CAAA,aAAU;QAChC,eAAe;YACb,SAAS;YACT,QAAQ;QACV;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;YACA;QACF;8BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;YACrB;QACF,EAAE,OAAO,OAAO;QACd,uDAAuD;QACzD;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,gEAAgE;YAChE,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,WAAmB,EAAE;gBAE3B,mCAAmC;gBACnC,KAAK,MAAM,QAAQ,KAAK,KAAK,CAAE;oBAC7B,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;wBACvC,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;4BAClB,SAAS,IAAI,CAAC;gCACZ,GAAG,IAAI;gCACP,MAAM;oCACJ,IAAI,KAAK,EAAE;oCACX,MAAM,KAAK,IAAI;oCACf,OAAO,KAAK,KAAK;gCACnB;4BACF;wBACF;oBACF;gBACF;gBAEA,uCAAuC;gBACvC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACvF,SAAS;YACX;QACF,EAAE,OAAO,OAAO;QACd,uDAAuD;QACzD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,qBAA2B,OAAP;YAClD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO;YACT;QACF,EAAE,OAAO,OAAO;QACd,gEAAgE;QAClE;QACA,OAAO,EAAE;IACX;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,sBAAsB;gBACtB,KAAK,KAAK;YACZ;QACF,EAAE,OAAO,OAAO;QACd,sDAAsD;QACxD;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI,CAAC,QAAQ,+CAA+C;QAE5D,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,cAAoB,OAAP,SAAU;gBACnD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;YACR;QACF,EAAE,OAAO,OAAO;QACd,sDAAsD;QACxD;IACF;IAEA,MAAM,gBAAgB,iBAClB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,kBACrC;IAEJ,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,sBAAsB;;0CAC3C,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMrC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAsB;;;;;;kDACvC,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;;0DAEjD,6LAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;oDAAqB,OAAO,KAAK,EAAE;;wDACjC,KAAK,IAAI;wDAAC;wDAAG,KAAK,KAAK;wDAAC;;mDADd,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWhC,6LAAC;gBAAI,WAAU;0BACZ,wBACC,6LAAC;8BAAE;;;;;2BACD,cAAc,MAAM,KAAK,kBAC3B,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;2BAIzC,cAAc,GAAG,CAAC,CAAC;wBAOJ,YAGC;yCATd,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,EAAA,aAAA,KAAK,IAAI,cAAT,iCAAA,WAAW,IAAI,KAAI;;;;;;kEAEtB,6LAAC;wDAAK,WAAU;;4DAAgC;4DAC5C,EAAA,cAAA,KAAK,IAAI,cAAT,kCAAA,YAAW,KAAK,KAAI;4DAAW;;;;;;;;;;;;;0DAGrC,6LAAC;gDAAE,WAAU;0DACV,KAAK,OAAO;;;;;;0DAEf,6LAAC;gDAAE,WAAU;0DACV,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc;;;;;;;;;;;;kDAG5C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,aAAa,KAAK,EAAE;kDAEnC,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uBAxBf,KAAK,EAAE;;;;;;;;;;;0BAkCxB,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc,CAAC;oBACb,IAAI,CAAC,MAAM;wBACT,sBAAsB;wBACtB,KAAK,KAAK;oBACZ;gBACF;0BAEA,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;sCACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;0CAAC;;;;;;;;;;;sCAEf,6LAAC,mIAAA,CAAA,OAAI;4BAAC,UAAU,KAAK,YAAY,CAAC;;8CAChC,6LAAC,mIAAA,CAAA,YAAS;;sDACR,6LAAC,mIAAA,CAAA,YAAS;4CAAC,SAAQ;sDAAS;;;;;;sDAC5B,6LAAC,qIAAA,CAAA,SAAM;4CAAE,GAAG,KAAK,QAAQ,CAAC,SAAS;;8DACjC,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;wDAAqB,OAAO,KAAK,EAAE;;4DACjC,KAAK,IAAI;4DAAC;4DAAG,KAAK,KAAK;4DAAC;;uDADd,KAAK,EAAE;;;;;;;;;;;wCAKvB,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,kBAC3B,6LAAC,mIAAA,CAAA,cAAW;sDAAE,KAAK,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO;;;;;;;;;;;;8CAItD,6LAAC,mIAAA,CAAA,YAAS;;sDACR,6LAAC,mIAAA,CAAA,YAAS;4CAAC,SAAQ;sDAAU;;;;;;sDAC7B,6LAAC;4CACC,IAAG;4CACH,MAAM;4CACN,aAAY;4CACZ,WAAU;4CACT,GAAG,KAAK,QAAQ,CAAC,UAAU;;;;;;wCAE7B,KAAK,SAAS,CAAC,MAAM,CAAC,OAAO,kBAC5B,6LAAC,mIAAA,CAAA,cAAW;sDAAE,KAAK,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAIvD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAS,WAAU;sDAAS;;;;;;sDAGzC,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;gDACP,sBAAsB;gDACtB,KAAK,KAAK;4CACZ;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAvQwB;;QAOT,iKAAA,CAAA,UAAO;;;KAPE", "debugId": null}}]}