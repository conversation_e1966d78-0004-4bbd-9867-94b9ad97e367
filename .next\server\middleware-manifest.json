{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_245a98c3._.js", "server/edge/chunks/[root-of-the-server]__07f7259f._.js", "server/edge/chunks/edge-wrapper_c439e1ea.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "j/hHicgoNX9HWG5VRphNHs9wDCx/Z/kndf6fG17Knw4=", "__NEXT_PREVIEW_MODE_ID": "359b339b3a944fe922158cf86a366966", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c9654df4dcecf1b389a45db3416f9aea302e4e40f1ab3577752e2452f8a45f62", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6a146329060c56ff13a5c04c7d2b5711ab21f8ae2c1787b8847ac71684ce7dda"}}}, "sortedMiddleware": ["/"], "functions": {}}