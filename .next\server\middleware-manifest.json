{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_245a98c3._.js", "server/edge/chunks/[root-of-the-server]__07f7259f._.js", "server/edge/chunks/edge-wrapper_c439e1ea.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "j/hHicgoNX9HWG5VRphNHs9wDCx/Z/kndf6fG17Knw4=", "__NEXT_PREVIEW_MODE_ID": "6b52a79da3905265cd9755cb4f80305c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "03495571943e6fab67948257ee9896c3009f75190761c32e30ad04d94d2d24ae", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cef2b94c01f2544eb5deae4d96018a7e27dcc8b8c323b128f7473dd9e9afcbd8"}}}, "sortedMiddleware": ["/"], "functions": {}}