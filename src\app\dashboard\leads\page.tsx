'use client'

import { useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { leadSchema, type LeadInput } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Form, FormField, FormLabel, FormMessage } from '@/components/ui/form'
import { Plus, Search, Filter, Edit, Trash2, Mail, Phone } from 'lucide-react'

interface Lead {
  id: string
  name: string
  location?: string
  phone?: string
  email: string
  website?: string
  notes?: string
  source?: string
  status: 'NEW' | 'CONTACTED' | 'INTERESTED' | 'CONVERTED' | 'LOST'
  createdAt: string
  updatedAt: string
  _count: {
    leadNotes: number
    followUps: number
  }
}

const statusColors = {
  NEW: 'bg-blue-500 text-white',
  CONTACTED: 'bg-gray-500 text-white',
  INTERESTED: 'bg-yellow-500 text-white',
  CONVERTED: 'bg-green-500 text-white',
  LOST: 'bg-red-500 text-white',
}

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingLead, setEditingLead] = useState<Lead | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [sourceFilter, setSourceFilter] = useState('')

  const form = useForm<LeadInput>({
    resolver: zodResolver(leadSchema),
    defaultValues: {
      name: '',
      location: '',
      phone: '',
      email: '',
      website: '',
      notes: '',
      source: '',
      status: 'NEW',
    },
  })

  useEffect(() => {
    fetchLeads()
  }, [statusFilter, sourceFilter])

  const fetchLeads = async () => {
    try {
      const params = new URLSearchParams()
      if (statusFilter) params.append('status', statusFilter)
      if (sourceFilter) params.append('source', sourceFilter)
      
      const response = await fetch(`/api/leads?${params}`)
      if (response.ok) {
        const data = await response.json()
        setLeads(data.leads)
      }
    } catch (error) {
      console.error('Error fetching leads:', error)
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: LeadInput) => {
    try {
      const url = editingLead ? `/api/leads/${editingLead.id}` : '/api/leads'
      const method = editingLead ? 'PUT' : 'POST'
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        await fetchLeads()
        setIsCreateDialogOpen(false)
        setIsEditDialogOpen(false)
        setEditingLead(null)
        form.reset()
      }
    } catch (error) {
      console.error('Error saving lead:', error)
    }
  }

  const handleEdit = (lead: Lead) => {
    setEditingLead(lead)
    form.reset({
      name: lead.name,
      location: lead.location || '',
      phone: lead.phone || '',
      email: lead.email,
      website: lead.website || '',
      notes: lead.notes || '',
      source: lead.source || '',
      status: lead.status,
    })
    setIsEditDialogOpen(true)
  }

  const handleDelete = async (leadId: string) => {
    if (!confirm('Are you sure you want to delete this lead?')) return

    try {
      const response = await fetch(`/api/leads/${leadId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await fetchLeads()
      }
    } catch (error) {
      console.error('Error deleting lead:', error)
    }
  }

  const filteredLeads = leads.filter(lead =>
    lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lead.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Leads</h1>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Lead
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search leads..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="">All Statuses</option>
              <option value="NEW">New</option>
              <option value="CONTACTED">Contacted</option>
              <option value="INTERESTED">Interested</option>
              <option value="CONVERTED">Converted</option>
              <option value="LOST">Lost</option>
            </Select>
            <Input
              placeholder="Filter by source..."
              value={sourceFilter}
              onChange={(e) => setSourceFilter(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Leads List */}
      <div className="grid gap-4">
        {loading ? (
          <p>Loading leads...</p>
        ) : filteredLeads.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-muted-foreground">No leads found.</p>
            </CardContent>
          </Card>
        ) : (
          filteredLeads.map((lead) => (
            <Card key={lead.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-semibold">{lead.name}</h3>
                      <Badge className={statusColors[lead.status]}>
                        {lead.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        {lead.email}
                      </div>
                      {lead.phone && (
                        <div className="flex items-center gap-1">
                          <Phone className="h-4 w-4" />
                          {lead.phone}
                        </div>
                      )}
                      {lead.location && (
                        <span>📍 {lead.location}</span>
                      )}
                      {lead.website && (
                        <span>🌐 {lead.website}</span>
                      )}
                      {lead.source && (
                        <span>Source: {lead.source}</span>
                      )}
                    </div>
                    {lead.notes && (
                      <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                        {lead.notes}
                      </div>
                    )}
                    <div className="text-xs text-muted-foreground">
                      {lead._count.leadNotes || 0} notes • {lead._count.followUps} follow-ups
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(lead)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(lead.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Create/Edit Dialog */}
      <Dialog 
        open={isCreateDialogOpen || isEditDialogOpen} 
        onOpenChange={(open) => {
          if (!open) {
            setIsCreateDialogOpen(false)
            setIsEditDialogOpen(false)
            setEditingLead(null)
            form.reset()
          }
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingLead ? 'Edit Lead' : 'Add New Lead'}
            </DialogTitle>
          </DialogHeader>
          <Form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField>
              <FormLabel htmlFor="name">Name</FormLabel>
              <Input
                id="name"
                placeholder="Enter lead name"
                {...form.register('name')}
              />
              {form.formState.errors.name && (
                <FormMessage>{form.formState.errors.name.message}</FormMessage>
              )}
            </FormField>

            <FormField>
              <FormLabel htmlFor="location">Location (Optional)</FormLabel>
              <Input
                id="location"
                placeholder="Enter location/address"
                {...form.register('location')}
              />
            </FormField>

            <FormField>
              <FormLabel htmlFor="phone">Phone (Optional)</FormLabel>
              <Input
                id="phone"
                placeholder="Enter phone number"
                {...form.register('phone')}
              />
            </FormField>

            <FormField>
              <FormLabel htmlFor="email">Email</FormLabel>
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                {...form.register('email')}
              />
              {form.formState.errors.email && (
                <FormMessage>{form.formState.errors.email.message}</FormMessage>
              )}
            </FormField>

            <FormField>
              <FormLabel htmlFor="website">Website (Optional)</FormLabel>
              <Input
                id="website"
                type="url"
                placeholder="https://example.com"
                {...form.register('website')}
              />
              {form.formState.errors.website && (
                <FormMessage>{form.formState.errors.website.message}</FormMessage>
              )}
            </FormField>

            <FormField>
              <FormLabel htmlFor="notes">Notes (Optional)</FormLabel>
              <textarea
                id="notes"
                rows={3}
                placeholder="Enter any notes about this lead..."
                className="flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                {...form.register('notes')}
              />
            </FormField>

            <FormField>
              <FormLabel htmlFor="source">Source (Optional)</FormLabel>
              <Input
                id="source"
                placeholder="e.g., Website, Referral, Cold Call"
                {...form.register('source')}
              />
            </FormField>

            <FormField>
              <FormLabel htmlFor="status">Status</FormLabel>
              <Select {...form.register('status')}>
                <option value="NEW">New</option>
                <option value="CONTACTED">Contacted</option>
                <option value="INTERESTED">Interested</option>
                <option value="CONVERTED">Converted</option>
                <option value="LOST">Lost</option>
              </Select>
            </FormField>

            <div className="flex gap-2 pt-4">
              <Button type="submit" className="flex-1">
                {editingLead ? 'Update Lead' : 'Create Lead'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateDialogOpen(false)
                  setIsEditDialogOpen(false)
                  setEditingLead(null)
                  form.reset()
                }}
              >
                Cancel
              </Button>
            </div>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
