{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/auth-edge.ts"], "sourcesContent": ["import { jwtVerify } from 'jose'\nimport { NextRequest } from 'next/server'\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key'\n\nexport interface JWTPayload {\n  userId: string\n  email: string\n}\n\nexport async function verifyTokenEdge(token: string): Promise<JWTPayload | null> {\n  try {\n    const secret = new TextEncoder().encode(JWT_SECRET)\n    const { payload } = await jwtVerify(token, secret)\n    \n    return {\n      userId: payload.userId as string,\n      email: payload.email as string,\n    }\n  } catch (error) {\n    return null\n  }\n}\n\nexport function getTokenFromRequest(request: NextRequest): string | null {\n  const authHeader = request.headers.get('authorization')\n  if (authHeader && authHeader.startsWith('Bearer ')) {\n    return authHeader.substring(7)\n  }\n  \n  // Also check for token in cookies\n  const token = request.cookies.get('auth-token')?.value\n  return token || null\n}\n\nexport async function getUserFromRequestEdge(request: NextRequest): Promise<JWTPayload | null> {\n  const token = getTokenFromRequest(request)\n  if (!token) return null\n  \n  return await verifyTokenEdge(token)\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAGA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAOtC,eAAe,gBAAgB,KAAa;IACjD,IAAI;QACF,MAAM,SAAS,IAAI,cAAc,MAAM,CAAC;QACxC,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAA,GAAA,+JAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAE3C,OAAO;YACL,QAAQ,QAAQ,MAAM;YACtB,OAAO,QAAQ,KAAK;QACtB;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAEO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;IACvC,IAAI,cAAc,WAAW,UAAU,CAAC,YAAY;QAClD,OAAO,WAAW,SAAS,CAAC;IAC9B;IAEA,kCAAkC;IAClC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,OAAO,SAAS;AAClB;AAEO,eAAe,uBAAuB,OAAoB;IAC/D,MAAM,QAAQ,oBAAoB;IAClC,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,MAAM,gBAAgB;AAC/B"}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getUserFromRequestEdge } from '@/lib/auth-edge'\n\nexport async function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // Skip middleware for static files and Next.js internals\n  if (\n    pathname.startsWith('/_next/') ||\n    pathname.startsWith('/api/_next/') ||\n    pathname.includes('favicon.ico') ||\n    pathname.includes('.') && !pathname.includes('/api/')\n  ) {\n    return NextResponse.next()\n  }\n\n  // Only protect API routes (except auth routes)\n  if (pathname.startsWith('/api/')) {\n    const publicApiRoutes = ['/api/auth/', '/api/test-auth', '/api/debug-auth']\n    const isPublicApiRoute = publicApiRoutes.some(route => pathname.startsWith(route))\n\n    if (!isPublicApiRoute) {\n      const user = await getUserFromRequestEdge(request)\n\n      if (!user) {\n        return NextResponse.json(\n          { error: 'Unauthorized' },\n          { status: 401 }\n        )\n      }\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     * - public folder\n     */\n    '/((?!_next/static|_next/image|favicon.ico|public/).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEO,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,yDAAyD;IACzD,IACE,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,QAAQ,CAAC,kBAClB,SAAS,QAAQ,CAAC,QAAQ,CAAC,SAAS,QAAQ,CAAC,UAC7C;QACA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,+CAA+C;IAC/C,IAAI,SAAS,UAAU,CAAC,UAAU;QAChC,MAAM,kBAAkB;YAAC;YAAc;YAAkB;SAAkB;QAC3E,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAA,QAAS,SAAS,UAAU,CAAC;QAE3E,IAAI,CAAC,kBAAkB;YACrB,MAAM,OAAO,MAAM,CAAA,GAAA,kIAAA,CAAA,yBAAsB,AAAD,EAAE;YAE1C,IAAI,CAAC,MAAM;gBACT,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAe,GACxB;oBAAE,QAAQ;gBAAI;YAElB;QACF;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}