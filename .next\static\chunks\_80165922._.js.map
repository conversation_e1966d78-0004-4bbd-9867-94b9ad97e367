{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,6JAAA,CAAA,aAAgB,MAG3B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,aAAgB,OAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,aAAgB,OAGhC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,aAAgB,OAGtC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,aAAgB,QAGjC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success:\n          \"border-transparent bg-green-500 text-white hover:bg-green-600\",\n        warning:\n          \"border-transparent bg-yellow-500 text-white hover:bg-yellow-600\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB,GAA5C;IACb,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/src/app/dashboard/import/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Upload, FileText, CheckCircle, XCircle, AlertCircle, Download } from 'lucide-react'\n\ninterface ImportResult {\n  success: boolean\n  imported: number\n  errors: Array<{\n    row: number\n    data: any\n    error: string\n  }>\n  duplicates: Array<{\n    row: number\n    email: string\n  }>\n}\n\nexport default function ImportPage() {\n  const [file, setFile] = useState<File | null>(null)\n  const [importing, setImporting] = useState(false)\n  const [result, setResult] = useState<ImportResult | null>(null)\n  const [skipDuplicates, setSkipDuplicates] = useState(true)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const selectedFile = event.target.files?.[0]\n    if (selectedFile) {\n      setFile(selectedFile)\n      setResult(null)\n    }\n  }\n\n  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault()\n    const droppedFile = event.dataTransfer.files[0]\n    if (droppedFile) {\n      setFile(droppedFile)\n      setResult(null)\n    }\n  }\n\n  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {\n    event.preventDefault()\n  }\n\n  const handleImport = async () => {\n    if (!file) return\n\n    setImporting(true)\n    try {\n      const formData = new FormData()\n      formData.append('file', file)\n      formData.append('skipDuplicates', skipDuplicates.toString())\n\n      const response = await fetch('/api/leads/bulk-import', {\n        method: 'POST',\n        body: formData,\n      })\n\n      const data = await response.json()\n      setResult(data)\n    } catch (error) {\n      console.error('Import error:', error)\n      setResult({\n        success: false,\n        imported: 0,\n        errors: [{ row: 0, data: {}, error: 'Failed to upload file' }],\n        duplicates: [],\n      })\n    } finally {\n      setImporting(false)\n    }\n  }\n\n  const downloadSampleCSV = () => {\n    const csvContent = `name,location,phone,email,website,notes,source\nJohn Doe,\"New York, NY\",+1234567890,<EMAIL>,https://johndoe.com,Interested in premium package,Website\nJane Smith,\"Los Angeles, CA\",+0987654321,<EMAIL>,,Referred by existing customer,Referral\nBob Johnson,\"Chicago, IL\",,<EMAIL>,https://bobjohnson.biz,Follow up next week,Cold Call`\n\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = 'sample-leads.csv'\n    a.click()\n    window.URL.revokeObjectURL(url)\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex items-center justify-between\">\n        <h1 className=\"text-3xl font-bold\">Import Leads</h1>\n        <Button variant=\"outline\" onClick={downloadSampleCSV}>\n          <Download className=\"mr-2 h-4 w-4\" />\n          Download Sample CSV\n        </Button>\n      </div>\n\n      {/* Instructions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Import Instructions</CardTitle>\n          <CardDescription>\n            Upload a CSV or Excel file with your lead data\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div>\n            <h4 className=\"font-medium mb-2\">Supported Columns:</h4>\n            <ul className=\"text-sm text-muted-foreground space-y-1\">\n              <li>• <strong>name</strong> - Lead's full name (required)</li>\n              <li>• <strong>location</strong> - Location/address (optional)</li>\n              <li>• <strong>phone</strong> - Phone number (optional)</li>\n              <li>• <strong>email</strong> - Email address (required)</li>\n              <li>• <strong>website</strong> - Website URL (optional)</li>\n              <li>• <strong>notes</strong> - Additional notes (optional)</li>\n              <li>• <strong>source</strong> - Lead source (optional)</li>\n            </ul>\n          </div>\n          <div>\n            <h4 className=\"font-medium mb-2\">Supported Formats:</h4>\n            <p className=\"text-sm text-muted-foreground\">\n              CSV (.csv), Excel (.xlsx, .xls)\n            </p>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* File Upload */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Upload File</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div\n            className=\"border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer\"\n            onDrop={handleDrop}\n            onDragOver={handleDragOver}\n            onClick={() => fileInputRef.current?.click()}\n          >\n            <Upload className=\"h-12 w-12 mx-auto mb-4 text-gray-400\" />\n            {file ? (\n              <div>\n                <p className=\"text-lg font-medium\">{file.name}</p>\n                <p className=\"text-sm text-muted-foreground\">\n                  {(file.size / 1024).toFixed(1)} KB\n                </p>\n              </div>\n            ) : (\n              <div>\n                <p className=\"text-lg font-medium\">Drop your file here</p>\n                <p className=\"text-sm text-muted-foreground\">\n                  or click to browse\n                </p>\n              </div>\n            )}\n          </div>\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\".csv,.xlsx,.xls\"\n            onChange={handleFileSelect}\n            className=\"hidden\"\n          />\n\n          {file && (\n            <div className=\"mt-4 space-y-4\">\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  id=\"skipDuplicates\"\n                  checked={skipDuplicates}\n                  onChange={(e) => setSkipDuplicates(e.target.checked)}\n                  className=\"rounded\"\n                />\n                <label htmlFor=\"skipDuplicates\" className=\"text-sm\">\n                  Skip duplicate emails (recommended)\n                </label>\n              </div>\n\n              <Button\n                onClick={handleImport}\n                disabled={importing}\n                className=\"w-full\"\n              >\n                {importing ? 'Importing...' : 'Import Leads'}\n              </Button>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Results */}\n      {result && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              {result.success ? (\n                <CheckCircle className=\"h-5 w-5 text-green-600\" />\n              ) : (\n                <XCircle className=\"h-5 w-5 text-red-600\" />\n              )}\n              Import Results\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">\n                  {result.imported}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">\n                  Successfully Imported\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-yellow-600\">\n                  {result.duplicates.length}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">\n                  Duplicates Found\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-red-600\">\n                  {result.errors.length}\n                </div>\n                <div className=\"text-sm text-muted-foreground\">\n                  Errors\n                </div>\n              </div>\n            </div>\n\n            {result.errors.length > 0 && (\n              <div>\n                <h4 className=\"font-medium mb-2 flex items-center gap-2\">\n                  <AlertCircle className=\"h-4 w-4 text-red-600\" />\n                  Errors\n                </h4>\n                <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                  {result.errors.map((error, index) => (\n                    <div key={index} className=\"text-sm p-2 bg-red-50 rounded\">\n                      <strong>Row {error.row}:</strong> {error.error}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {result.duplicates.length > 0 && (\n              <div>\n                <h4 className=\"font-medium mb-2 flex items-center gap-2\">\n                  <AlertCircle className=\"h-4 w-4 text-yellow-600\" />\n                  Duplicates {skipDuplicates ? '(Skipped)' : '(Failed)'}\n                </h4>\n                <div className=\"space-y-2 max-h-40 overflow-y-auto\">\n                  {result.duplicates.map((duplicate, index) => (\n                    <div key={index} className=\"text-sm p-2 bg-yellow-50 rounded\">\n                      <strong>Row {duplicate.row}:</strong> {duplicate.email} already exists\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {result.success && result.imported > 0 && (\n              <div className=\"text-center\">\n                <Badge className=\"bg-green-500 text-white\">\n                  Import completed successfully!\n                </Badge>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAsBe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAC1D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;YACH;QAArB,MAAM,gBAAe,sBAAA,MAAM,MAAM,CAAC,KAAK,cAAlB,0CAAA,mBAAoB,CAAC,EAAE;QAC5C,IAAI,cAAc;YAChB,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc;QACpB,MAAM,cAAc,MAAM,YAAY,CAAC,KAAK,CAAC,EAAE;QAC/C,IAAI,aAAa;YACf,QAAQ;YACR,UAAU;QACZ;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc;IACtB;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,kBAAkB,eAAe,QAAQ;YAEzD,MAAM,WAAW,MAAM,MAAM,0BAA0B;gBACrD,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,UAAU;gBACR,SAAS;gBACT,UAAU;gBACV,QAAQ;oBAAC;wBAAE,KAAK;wBAAG,MAAM,CAAC;wBAAG,OAAO;oBAAwB;iBAAE;gBAC9D,YAAY,EAAE;YAChB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,MAAM,aAAc;QAKpB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG;QACb,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;;0CACjC,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;0CACX,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAa;;;;;;;0DAC3B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAiB;;;;;;;0DAC/B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAc;;;;;;;0DAC5B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAc;;;;;;;0DAC5B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAgB;;;;;;;0DAC9B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAc;;;;;;;0DAC5B,6LAAC;;oDAAG;kEAAE,6LAAC;kEAAO;;;;;;oDAAe;;;;;;;;;;;;;;;;;;;0CAGjC,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmB;;;;;;kDACjC,6LAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCACC,WAAU;gCACV,QAAQ;gCACR,YAAY;gCACZ,SAAS;wCAAM;4CAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,KAAK;;;kDAE1C,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACjB,qBACC,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAuB,KAAK,IAAI;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;;oDACV,CAAC,KAAK,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;6DAInC,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAsB;;;;;;0DACnC,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAMnD,6LAAC;gCACC,KAAK;gCACL,MAAK;gCACL,QAAO;gCACP,UAAU;gCACV,WAAU;;;;;;4BAGX,sBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS;gDACT,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,OAAO;gDACnD,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAiB,WAAU;0DAAU;;;;;;;;;;;;kDAKtD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAQvC,wBACC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;gCAClB,OAAO,OAAO,iBACb,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCACnB;;;;;;;;;;;;kCAIN,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ;;;;;;0DAElB,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAIjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,OAAO,UAAU,CAAC,MAAM;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;kDAIjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,CAAC,MAAM;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;4BAMlD,OAAO,MAAM,CAAC,MAAM,GAAG,mBACtB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAAyB;;;;;;;kDAGlD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzB,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;;4DAAO;4DAAK,MAAM,GAAG;4DAAC;;;;;;;oDAAU;oDAAE,MAAM,KAAK;;+CADtC;;;;;;;;;;;;;;;;4BAQjB,OAAO,UAAU,CAAC,MAAM,GAAG,mBAC1B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CAA4B;4CACvC,iBAAiB,cAAc;;;;;;;kDAE7C,6LAAC;wCAAI,WAAU;kDACZ,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBACjC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;;4DAAO;4DAAK,UAAU,GAAG;4DAAC;;;;;;;oDAAU;oDAAE,UAAU,KAAK;oDAAC;;+CAD/C;;;;;;;;;;;;;;;;4BAQjB,OAAO,OAAO,IAAI,OAAO,QAAQ,GAAG,mBACnC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D;GArQwB;KAAA", "debugId": null}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js", "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-x.js", "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/node_modules/lucide-react/src/icons/circle-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm15 9-6 6', key: '1uzhvr' }],\n  ['path', { d: 'm9 9 6 6', key: 'z0biqf' }],\n];\n\n/**\n * @component @name CircleX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleX = createLucideIcon('circle-x', __iconNode);\n\nexport default CircleX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js", "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/node_modules/lucide-react/src/icons/circle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '12', x2: '12', y1: '8', y2: '12', key: '1pkeuh' }],\n  ['line', { x1: '12', x2: '12.01', y1: '16', y2: '16', key: '4dfq90' }],\n];\n\n/**\n * @component @name CircleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjgiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMi4wMSIgeTE9IjE2IiB5Mj0iMTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleAlert = createLucideIcon('circle-alert', __iconNode);\n\nexport default CircleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACvE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/download.js", "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/leads/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 15V3', key: 'm9g1x1' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['path', { d: 'm7 10 5 5 5-5', key: 'brsn70' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTVWMyIgLz4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('download', __iconNode);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}